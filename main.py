# 导入必要的库
# maix系列库用于图像处理、显示、应用管理、时间控制和摄像头操作
from maix import image, display, app, time, camera
# OpenCV库用于图像处理和计算机视觉功能
import cv2
# NumPy库用于数值计算和数组操作
import numpy as np
# math库用于数学计算
import math
# 导入串口通信相关库，用于串口数据发送
from micu_uart_lib import (
    SimpleUART, micu_printf
)

# --------------------------- 紫色激光检测器类 ---------------------------
class PurpleLaserDetector:
    """
    紫色激光检测器类，用于识别图像中的紫色激光点
    """
    def __init__(self, pixel_radius=3):
        """
        初始化检测器
        :param pixel_radius: 激光点识别的像素半径
        """
        self.pixel_radius = pixel_radius
        # 创建3x3的卷积核，用于后续的形态学操作
        self.kernel = np.ones((3, 3), np.uint8)
        
    def detect(self, img):
        """
        检测图像中的紫色激光点
        :param img: 输入图像（BGR格式）
        :return: 标记了激光点的图像和激光点坐标列表
        """
        # 将BGR图像转换为HSV颜色空间，便于颜色过滤
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        
        # 定义紫色的HSV范围（下限和上限）
        lower_purple = np.array([130, 80, 80])
        upper_purple = np.array([160, 255, 255])
        
        # 根据HSV范围创建紫色掩码（只保留紫色区域）
        mask_purple = cv2.inRange(hsv, lower_purple, upper_purple)
        
        # 对掩码进行闭运算，消除小的空洞和噪声
        mask_purple = cv2.morphologyEx(mask_purple, cv2.MORPH_CLOSE, self.kernel)
        
        # 从掩码中寻找轮廓
        contours_purple, _ = cv2.findContours(mask_purple, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        laser_points = []  # 存储检测到的激光点坐标
        
        # 遍历所有找到的轮廓
        for cnt in contours_purple:
            # 计算轮廓的最小外接矩形
            rect = cv2.minAreaRect(cnt)
            # 获取矩形中心坐标作为激光点坐标
            cx, cy = map(int, rect[0])
            laser_points.append((cx, cy))
            
            # 在图像上标记激光点（紫色圆圈）
            cv2.circle(img, (cx, cy), 3, (255, 0, 255), -1)
            # 在激光点附近添加文字标签
            cv2.putText(img, "Laser", (cx-20, cy-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 0, 255), 1)
        
        # 返回标记了激光点的图像和激光点坐标列表
        return img, laser_points

# --------------------------- 圆形轨迹点生成函数 ---------------------------
def generate_circle_points(center, radius, num_points):
    """
    在校正后的矩形内生成圆形轨迹点
    :param center: 圆心坐标 (x, y)
    :param radius: 圆的半径
    :param num_points: 生成的点数量
    :return: 圆周上均匀分布的点坐标列表
    """
    circle_points = []
    cx, cy = center
    # 计算圆周上均匀分布的点
    for i in range(num_points):
        # 计算角度（0到2π）
        angle = 2 * math.pi * i / num_points
        # 计算点的x坐标
        x = int(cx + radius * math.cos(angle))
        # 计算点的y坐标
        y = int(cy + radius * math.sin(angle))
        circle_points.append((x, y))
    return circle_points

# --------------------------- 透视变换工具函数 ---------------------------
def perspective_transform(pts, target_width, target_height):
    """
    对四边形进行透视变换，将倾斜的四边形校正为正矩形
    :param pts: 四边形顶点坐标 (4,2) 格式的数组
    :param target_width: 校正后矩形的宽度
    :param target_height: 校正后矩形的高度
    :return: 变换矩阵M、逆变换矩阵M_inv和排序后的源点
    """
    # 顶点排序（左上→右上→右下→左下）
    # 计算每个点的横纵坐标之和，和最小的是左上，和最大的是右下
    s = pts.sum(axis=1)
    tl = pts[np.argmin(s)]  # 左上
    br = pts[np.argmax(s)]  # 右下
    
    # 计算每个点的横纵坐标之差，差最小的是右上，差最大的是左下
    diff = np.diff(pts, axis=1)
    tr = pts[np.argmin(diff)]  # 右上
    bl = pts[np.argmax(diff)]  # 左下
    
    # 排序后的源点坐标
    src_pts = np.array([tl, tr, br, bl], dtype=np.float32)
    
    # 目标矩形的四个顶点坐标
    dst_pts = np.array([
        [0, 0], [target_width-1, 0],          # 左上、右上
        [target_width-1, target_height-1], [0, target_height-1]  # 右下、左下
    ], dtype=np.float32)
    
    # 计算透视变换矩阵
    M = cv2.getPerspectiveTransform(src_pts, dst_pts)
    # 计算逆变换矩阵（用于将校正后的点映射回原图）
    ret, M_inv = cv2.invert(M)
    return M, M_inv, src_pts

# --------------------------- 主程序 ---------------------------
if __name__ == "__main__":
    # 初始化显示设备
    disp = display.Display()
    # 初始化摄像头，设置分辨率为180x120，格式为BGR888
    cam = camera.Camera(180, 120, image.Format.FMT_BGR888)
    # 创建紫色激光检测器实例
    laser_detector = PurpleLaserDetector()

    # 初始化串口通信
    uart = SimpleUART()
    if uart.init("/dev/ttyS0", 115200, set_as_global=True):
        print("串口初始化成功")
        # 禁用帧格式，使用原始数据发送
        uart.set_frame("", "", False)
    else:
        print("串口初始化失败")
        exit()  # 初始化失败则退出程序

    # 核心参数设置
    min_contour_area = 1000    # 轮廓最小面积（过滤小噪声）
    max_contour_area = 10000   # 轮廓最大面积（过滤过大区域）
    target_sides = 4           # 目标形状的边数（这里是四边形）
    
    # 透视变换与圆形参数
    corrected_width = 200      # 校正后矩形宽度
    corrected_height = 150     # 校正后矩形高度
    circle_radius = 40         # 校正后矩形内圆的半径
    circle_num_points = 12     # 圆周上的点数量
    
    # FPS计算初始化
    fps = 0                    # 帧率
    last_time = time.ticks_ms()# 上一帧的时间戳
    
    # 绘制参数（避免干扰识别区域）
    DRAW_PADDING = 10          # 绘制边距
    FPS_POSITION = (0, 10)     # FPS显示位置(左上角)
    TEXT_FONT = cv2.FONT_HERSHEY_SIMPLEX  # 字体
    TEXT_SCALE = 0.5           # 字体大小
    TEXT_THICKNESS = 1         # 字体粗细
    TEXT_COLOR = (0, 255, 0)   # 文字颜色（绿色）

    # 主循环，app.need_exit()用于检测程序退出信号
    while not app.need_exit():
        # 计算FPS（帧率）
        current_time = time.ticks_ms()  # 当前时间戳
        if current_time - last_time > 0:
            fps = 1000.0 / (current_time - last_time)  # 计算帧率
        last_time = current_time  # 更新上一帧时间戳
        
        # 读取摄像头图像
        img = cam.read()
        # 将maix图像转换为OpenCV格式（BGR）
        img_cv = image.image2cv(img, ensure_bgr=True, copy=False)
        # 创建输出图像副本（用于绘制结果，不修改原图）
        output = img_cv.copy()

        # 1. 矩形检测
        # 将图像转换为灰度图，便于轮廓检测
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        # 二值化处理，将灰度图转换为黑白图像（阈值46）
        _, binary = cv2.threshold(gray, 46, 255, cv2.THRESH_BINARY)
        # 寻找图像中的轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
        
        quads = []  # 存储符合条件的四边形
        # 遍历所有找到的轮廓
        for cnt in contours:
            # 计算轮廓面积
            area = cv2.contourArea(cnt)
            # 过滤面积在指定范围内的轮廓
            if min_contour_area < area < max_contour_area:
                # 多边形逼近（将轮廓近似为多边形）
                epsilon = 0.03 * cv2.arcLength(cnt, True)  # 逼近精度
                approx = cv2.approxPolyDP(cnt, epsilon, True)
                # 筛选出四边形（边数为4）
                if len(approx) == target_sides:
                    quads.append((approx, area))  # 存储轮廓和面积

        # 只保留最大的矩形（面积最大的四边形）
        inner_quads = []
        if quads:  # 如果找到四边形
            largest_quad = max(quads, key=lambda x: x[1])  # 按面积取最大
            inner_quads = [largest_quad]  # 存储最大四边形

        # 2. 处理内框：透视变换→画圆→映射回原图
        all_circle_points = []  # 存储所有映射回原图的圆轨迹点
        # 遍历找到的内框
        for approx, area in inner_quads:
            # 提取四边形顶点，转换为(4,2)格式
            pts = approx.reshape(4, 2).astype(np.float32)
            
            # 计算透视变换矩阵
            M, M_inv, src_pts = perspective_transform(
                pts, corrected_width, corrected_height
            )
            
            # 生成校正后矩形内的圆形轨迹（圆心为校正后矩形的中心）
            corrected_center = (corrected_width//2, corrected_height//2)  # 校正后中心
            corrected_circle = generate_circle_points(
                corrected_center, circle_radius, circle_num_points
            )
            
            # 将校正后的圆轨迹点映射回原图
            if M_inv is not None:  # 如果逆矩阵存在
                # 格式转换为opencv需要的形状 (1, N, 2)
                corrected_points_np = np.array([corrected_circle], dtype=np.float32)
                # 应用逆透视变换，将校正后的点映射回原图
                original_points = cv2.perspectiveTransform(corrected_points_np, M_inv)[0]
                # 转换为整数坐标
                original_points = [(int(x), int(y)) for x, y in original_points]
                all_circle_points.extend(original_points)  # 保存所有点
                
                # 绘制映射回原图的轨迹点（红色）
                for (x, y) in original_points:
                    cv2.circle(output, (x, y), 2, (0, 0, 255), -1)
            
            # 绘制内框轮廓和中心点（调试用）
            cv2.drawContours(output, [approx], -1, (0, 255, 0), 2)  # 绿色轮廓
            # 计算轮廓矩（用于求中心）
            M_moments = cv2.moments(approx)
            if M_moments["m00"] != 0:  # 避免除以零
                # 计算中心坐标
                cx = int(M_moments["m10"] / M_moments["m00"])
                cy = int(M_moments["m01"] / M_moments["m00"])
                cv2.circle(output, (cx, cy), 5, (255, 0, 0), -1)  # 蓝色中心点

        # 3. 激光检测
        output, laser_points = laser_detector.detect(output)

        # 4. 串口发送数据
        # 发送内框圆轨迹点中心作为MID（格式：MID:(x,y)）
        if all_circle_points:  # 如果有圆轨迹点
            # 计算所有圆轨迹点的中心坐标（平均值）
            center_x = sum(point[0] for point in all_circle_points) // len(all_circle_points)
            center_y = sum(point[1] for point in all_circle_points) // len(all_circle_points)
            mid_data = f"MID:({center_x},{center_y})"  # 格式化数据
            micu_printf(mid_data)  # 发送数据

        time.sleep(0.005)
        
        # 发送激光点作为TARGET（格式：TARGET:(x,y)）
        if laser_points:  # 如果检测到激光点
            # 如果有多个激光点，发送第一个点作为目标
            target_x, target_y = laser_points[0]
            target_data = f"TARGET:({target_x},{target_y})"  # 格式化数据
            micu_printf(target_data)  # 发送数据

        # 在左上角显示FPS，避免干扰主要识别区域
        cv2.putText(output, f"FPS: {fps:.1f}", FPS_POSITION,
                   TEXT_FONT, TEXT_SCALE, TEXT_COLOR, TEXT_THICKNESS)

        # 短暂延时，降低CPU占用
        time.sleep(0.005)

        # 显示图像
        # 将OpenCV图像转换为maix图像格式
        img_show = image.cv2image(output, bgr=True, copy=False)
        disp.show(img_show)  # 在显示屏上显示